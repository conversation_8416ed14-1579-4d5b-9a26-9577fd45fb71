{"name": "infotik-chat-socket", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nest start", "start:dev": "nest start --watch", "start:prod": "node dist/main.js", "build": "nest build", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://marsdevs-gairick-saha:<EMAIL>/infotik/infotik-chat-socket.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/infotik/infotik-chat-socket/issues"}, "homepage": "https://github.com/infotik/infotik-chat-socket#readme", "description": "", "dependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/common": "^11.1.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@nestjs/platform-socket.io": "^11.1.2", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.2", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "mongoose": "^8.15.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@nestjs/cli": "^11.0.7"}}