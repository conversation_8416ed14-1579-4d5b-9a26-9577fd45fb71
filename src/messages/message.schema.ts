import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Message document type
export type MessageDocument = Message & Document;

@Schema({ timestamps: true })
export class Message {
  // Reference to the room
  @Prop({ type: Types.ObjectId, ref: 'Room', required: true })
  room: Types.ObjectId;

  // Reference to the sender (user)
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  sender: Types.ObjectId;

  // Message content
  @Prop({ required: true })
  content: string;

  // Delivery status (sent, delivered, read)
  @Prop({ default: 'sent', enum: ['sent', 'delivered', 'read'] })
  status: 'sent' | 'delivered' | 'read';

  // Message type (text, image, video, etc.)
  @Prop({ default: 'text', enum: ['text', 'image', 'video', 'audio'] })
  type: 'text' | 'image' | 'video' | 'audio';

  // Message metadata
  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

export const MessageSchema = SchemaFactory.createForClass(Message);
