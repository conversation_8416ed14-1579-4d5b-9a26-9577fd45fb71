import { ConsoleLogger, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Message, MessageDocument } from './message.schema';

@Injectable()
export class MessagesService {
  constructor(
    @InjectModel(Message.name) private messageModel: Model<MessageDocument>,
    private readonly logger: ConsoleLogger,
  ) {}

  // Get chat history for a room (with pagination)
  async getMessages(roomId: string, limit = 20, skip = 0) {
    return this.messageModel
      .find({ room: roomId })
      .sort({ createdAt: 1 })
      .skip(skip)
      .limit(limit)
      .exec();
  }

  // Send a message to a room
  async sendMessage(roomId: string, senderId: string, content: string) {
    this.logger.log(`Sending message to room: ${content}`);
    const message = new this.messageModel({
      room: roomId,
      sender: senderId,
      content,
      status: 'sent',
      type: 'text',
      metadata: {
        roomId: roomId,
        senderId: senderId,
      },
    });
    await message.save();
    return message;
  }
}
