import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { MessagesService } from './messages.service';
import { RoomsService } from '../rooms/rooms.service';
import { UnauthorizedException } from '@nestjs/common';
import { Types } from 'mongoose';

/**
 * WebSocket Gateway for real-time chat (private and group)
 *
 * Namespace: /chat
 *
 * ---
 *
 * ## Events
 *
 * ### room:join
 * - Join an existing private/group chat room and fetch initial messages
 * - Payload: { type: 'private' | 'group', roomId: string, limit?: number }
 *   - roomId is required. You can only join existing rooms you are a participant of.
 * - Response: { room, messages }
 *
 * ### room:leave
 * - Leave a chat room
 * - Payload: roomId: string
 * - Response: room:left (roomId)
 *
 * ### message:send
 * - Send a message to a room
 * - Payload: { roomId: string, content: string }
 * - Response: message:delivered (messageId), message:new (to room)
 *
 * ### room:typing
 * - Typing indicator
 * - Payload: { roomId: string }
 * - Response: room:typing (to room)
 *
 * ### room:joined
 * - Emitted after successful join with room info and messages
 * - Payload: { room, messages }
 *
 * ### message:new
 * - Emitted to room when a new message is sent
 * - Payload: message object
 *
 * ### user:online / user:offline
 * - Emitted when a user connects/disconnects
 * - Payload: userId
 */
@WebSocketGateway({ namespace: '/chat', cors: true })
export class MessagesGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer() server: Server;

  private onlineUsers = new Map<string, string>(); // userId -> socketId

  constructor(
    private readonly jwtService: JwtService,
    private readonly messagesService: MessagesService,
    private readonly roomsService: RoomsService,
  ) {}

  // Handle new socket connection
  async handleConnection(client: Socket) {
    try {
      const token = client.handshake.headers?.authorization;
      if (!token) throw new UnauthorizedException('No token provided');
      const payload = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET,
      });
      client.data.user = payload;
      this.onlineUsers.set(payload.sub, client.id);
      this.server.emit('user:online', payload.sub);
    } catch (e) {
      client.disconnect();
    }
  }

  // Handle socket disconnect
  handleDisconnect(client: Socket) {
    const userId = client.data.user?.sub;
    if (userId) {
      this.onlineUsers.delete(userId);
      this.server.emit('user:offline', userId);
    }
  }

  // Unified join/create room (private or group) and fetch initial messages
  @SubscribeMessage('room:join')
  async handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: {
      type: 'private' | 'group';
      userId?: string; // for private
      name?: string; // for group
      participants?: string[]; // for group
      roomId?: string;
      limit?: number;
    },
  ) {
    const myId = client.data.user.sub;
    let room;

    if (data.roomId) {
      // Join existing room logic
      room = await this.roomsService.getUserRoomById(data.roomId, myId);
      if (!room) {
        client.emit('room:error', {
          message: 'Room not found or access denied.',
        });
        return;
      }
      const roomId = String(room._id);
      if (client.rooms.has(roomId)) {
        client.emit('room:error', {
          message: 'You have already joined this room.',
        });
        return;
      }
      if (!room.participants.includes(myId)) {
        client.emit('room:error', {
          message: 'You are not a participant of this room.',
        });
        return;
      }
      await client.join(roomId);
      const limit = data.limit || 20;
      const messages = await this.messagesService.getMessages(roomId, limit);
      client.emit('room:joined', { room, messages });
    } else {
      // No roomId: create a new room and join as owner
      if (data.type === 'private') {
        // For private, require userId (other participant)
        if (!data.userId || myId === data.userId) {
          client.emit('room:error', {
            message:
              'A valid userId (not yourself) is required to create a private room.',
          });
          return;
        }
        room = await this.roomsService.getOrCreatePrivateRoom(
          myId,
          data.userId,
        );
      } else if (data.type === 'group') {
        // For group, require name and participants (add self as owner/participant)
        const groupName = data.name || 'Group';
        const participants = Array.isArray(data.participants)
          ? data.participants
          : [];
        // Ensure the creator is included
        if (!participants.includes(myId)) participants.push(myId);
        room = await this.roomsService.createGroupRoom(
          groupName,
          myId,
          participants,
        );
      } else {
        client.emit('room:error', {
          message: 'Invalid room type or missing fields.',
        });
        return;
      }
      const roomId = String(room._id);
      await client.join(roomId);
      const limit = data.limit || 20;
      const messages = await this.messagesService.getMessages(roomId, limit);
      client.emit('room:joined', { room, messages });
    }
  }

  // Leave a chat room
  @SubscribeMessage('room:leave')
  async handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() roomId: string,
  ) {
    await client.leave(roomId);
    client.emit('room:left', roomId);
  }

  // Send a message
  @SubscribeMessage('message:send')
  async handleSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { roomId: string; content: string },
  ) {
    const senderId = client.data.user.sub;

    // Validate roomId
    if (
      !data.roomId ||
      typeof data.roomId !== 'string' ||
      !data.roomId.trim() ||
      !Types.ObjectId.isValid(data.roomId)
    ) {
      client.emit('message:error', {
        message: 'roomId is required and must be a valid ObjectId string.',
      });
      return;
    }

    // Validate content
    if (
      !data.content ||
      typeof data.content !== 'string' ||
      !data.content.trim()
    ) {
      client.emit('message:error', {
        message: 'Message content is required and must be a non-empty string.',
      });
      return;
    }

    try {
      // Check if roomId is valid and user is a participant
      const room = await this.roomsService.getUserRoomById(
        data.roomId,
        senderId,
      );
      if (!room) {
        client.emit('message:error', {
          message: 'Invalid roomId or access denied.',
        });
        return;
      }
      if (!room.participants.includes(senderId)) {
        client.emit('message:error', {
          message: 'You are not a participant of this room.',
        });
        return;
      }

      const message = await this.messagesService.sendMessage(
        data.roomId,
        senderId,
        data.content,
      );

      this.server.to(data.roomId).emit('message:new', {
        message: message,
      });
      client.emit('message:delivered', {
        message: message._id,
      });
    } catch (error) {
      client.emit('message:error', error);
      return;
    }
  }

  // Typing indicator (optional)
  @SubscribeMessage('room:typing')
  async handleTyping(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { roomId: string },
  ) {
    client
      .to(data.roomId)
      .emit('room:typing', { userId: client.data.user.sub });
  }
}
