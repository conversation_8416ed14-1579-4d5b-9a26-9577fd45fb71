import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Message, MessageSchema } from './message.schema';
import { MessagesService } from './messages.service';
import { MessagesController } from './messages.controller';
import { MessagesGateway } from './messages.gateway';
import { JwtService } from '@nestjs/jwt';
import { RoomsModule } from '../rooms/rooms.module';
import { LoggerModule } from '../common/LoggerModule';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Message.name, schema: MessageSchema }]),
    RoomsModule,
    LoggerModule,
  ],
  providers: [MessagesService, MessagesGateway, JwtService],
  controllers: [MessagesController],
  exports: [MongooseModule, MessagesService],
})
export class MessagesModule {}
