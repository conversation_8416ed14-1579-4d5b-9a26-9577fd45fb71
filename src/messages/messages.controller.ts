import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { MessagesService } from './messages.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('Messages')
@ApiBearerAuth()
@Controller('api/messages')
@UseGuards(JwtAuthGuard)
export class MessagesController {
  constructor(private readonly messagesService: MessagesService) {}

  // GET /api/messages/:roomId - Get chat history for a room (paginated)
  @ApiOperation({ summary: 'Get chat history for a room (paginated)' })
  @ApiQuery({ name: 'page', required: false, example: 1 })
  @ApiQuery({ name: 'limit', required: false, example: 20 })
  @ApiResponse({
    status: 200,
    description: 'List of messages',
    schema: {
      example: [
        {
          _id: '...',
          room: '...',
          sender: '...',
          content: 'Hello!',
          status: 'sent',
          createdAt: '...',
          updatedAt: '...',
        },
      ],
    },
  })
  @Get(':roomId')
  async getMessages(
    @Param('roomId') roomId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
  ) {
    const skip = (Number(page) - 1) * Number(limit);
    return this.messagesService.getMessages(roomId, Number(limit), skip);
  }
}
