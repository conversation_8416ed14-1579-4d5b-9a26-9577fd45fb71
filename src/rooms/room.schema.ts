import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

// Room document type
export type RoomDocument = Room & Document;

@Schema({ timestamps: true })
export class Room {
  // Room name (optional for private chats)
  @Prop()
  name?: string;

  // Room type: 'private' or 'group'
  @Prop({ required: true, enum: ['private', 'group'] })
  type: 'private' | 'group';

  // Array of participant user IDs
  @Prop({ type: [Types.ObjectId], ref: 'User', required: true })
  participants: Types.ObjectId[];

  // Admin user ID (reference to User)
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  admin: Types.ObjectId;
}

export const RoomSchema = SchemaFactory.createForClass(Room);
