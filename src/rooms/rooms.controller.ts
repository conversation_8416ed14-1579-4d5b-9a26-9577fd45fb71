import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { RoomsService } from './rooms.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('Rooms')
@ApiBearerAuth()
@Controller('api/rooms')
@UseGuards(JwtAuthGuard)
export class RoomsController {
  constructor(private readonly roomsService: RoomsService) {}

  // GET /api/rooms - Get user's chat rooms
  @ApiOperation({ summary: "Get user's chat rooms (paginated)" })
  @ApiQuery({
    name: 'page',
    required: false,
    example: 1,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    example: 20,
    description: 'Number of rooms per page (default: 20)',
  })
  @ApiResponse({
    status: 200,
    description:
      'Paginated list of rooms where the user is owner or participant',
    schema: {
      example: {
        total: 42,
        page: 1,
        limit: 20,
        rooms: [
          {
            _id: '...',
            name: 'Group',
            type: 'group',
            participants: ['...'],
            createdAt: '...',
            updatedAt: '...',
          },
        ],
      },
    },
  })
  @Get()
  async getRooms(@Req() req) {
    // Get pagination params from query (default: page=1, limit=20)
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 20;
    // Only return rooms where the user is owner or participant
    return this.roomsService.getUserRoomsPaginated(
      req.user.userId,
      page,
      limit,
    );
  }
}
