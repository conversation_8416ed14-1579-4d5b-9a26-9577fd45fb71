import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Room, RoomDocument } from './room.schema';

@Injectable()
export class RoomsService {
  constructor(@InjectModel(Room.name) private roomModel: Model<RoomDocument>) {}

  // Get all rooms for a user
  async getUserRooms(userId: string) {
    return this.roomModel.find({ participants: userId }).exec();
  }

  // Get a group room by ID and ensure the user is a participant
  async getUserRoomById(roomId: string, userId: string) {
    try {
      const room = await this.roomModel.findOne({
        _id: roomId,
        participants: userId,
      });
      if (!room) throw new NotFoundException('Room not found or access denied');
      return room;
    } catch (error) {
      throw error;
    }
  }

  // Create a new group chat room (participants must include creator)
  async createGroupRoom(
    name: string,
    creatorId: string,
    participantIds: string[],
  ) {
    const participants = [
      creatorId,
      ...participantIds.filter((id) => id !== creatorId),
    ];
    const room = new this.roomModel({
      name,
      type: 'group',
      participants,
      admin: creatorId,
    });
    await room.save();
    return room;
  }

  // Add participant to group
  async addParticipant(roomId: string, userId: string) {
    const room = await this.roomModel.findById(roomId);
    if (!room) throw new NotFoundException('Room not found');
    if (room.type !== 'group') throw new ForbiddenException('Not a group room');
    if (!room.participants.includes(userId as any)) {
      room.participants.push(userId as any);
      await room.save();
    }
    return room;
  }

  // Remove participant from group
  async removeParticipant(roomId: string, userId: string) {
    const room = await this.roomModel.findById(roomId);
    if (!room) throw new NotFoundException('Room not found');
    if (room.type !== 'group') throw new ForbiddenException('Not a group room');
    room.participants = room.participants.filter(
      (id) => id.toString() !== userId,
    );
    await room.save();
    return room;
  }

  // Join a group chat
  async joinRoom(roomId: string, userId: string) {
    return this.addParticipant(roomId, userId);
  }

  // Leave a group chat
  async leaveRoom(roomId: string, userId: string) {
    return this.removeParticipant(roomId, userId);
  }

  // Get or create a private 1v1 chat room between two users
  async getOrCreatePrivateRoom(userId1: string, userId2: string) {
    // Sort user IDs to ensure uniqueness regardless of order
    const participants = [userId1, userId2].sort();
    let room = await this.roomModel.findOne({
      type: 'private',
      participants: { $all: participants, $size: 2 },
    });
    if (!room) {
      room = new this.roomModel({
        type: 'private',
        participants,
        admin: userId1,
      });
      await room.save();
    }
    return room;
  }

  // Get paginated rooms for a user (owner or participant)
  async getUserRoomsPaginated(userId: string, page: number, limit: number) {
    const skip = (page - 1) * limit;
    // Find rooms where user is a participant or owner (assuming owner is in participants)
    const query = { participants: userId };
    const [rooms, total] = await Promise.all([
      this.roomModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .populate('admin')
        .exec(),
      this.roomModel.countDocuments(query),
    ]);
    return {
      total,
      page,
      limit,
      rooms,
    };
  }
}
