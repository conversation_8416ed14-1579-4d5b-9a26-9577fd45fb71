import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from './user.schema';

@Injectable()
export class UsersService {
  constructor(@InjectModel(User.name) private userModel: Model<UserDocument>) {}

  // Find user by ID
  async findById(id: string) {
    return this.userModel.findById(id).exec();
  }

  // Find user by username
  async findByUsername(username: string) {
    return this.userModel.findOne({ username }).exec();
  }

  // Get all users
  async findAll() {
    return this.userModel.find().exec();
  }
}
