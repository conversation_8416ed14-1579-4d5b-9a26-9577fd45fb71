import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// User document type
export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  // Unique username for the user
  @Prop({ required: true, unique: true })
  username: string;

  // Hashed password
  @Prop({ required: true })
  password: string;

  // Optional metadata (profile info, etc.)
  @Prop({ type: Object, default: {} })
  metadata?: Record<string, any>;
}

export const UserSchema = SchemaFactory.createForClass(User);
