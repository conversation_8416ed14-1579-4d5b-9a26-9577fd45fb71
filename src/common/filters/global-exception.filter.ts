import {
  ExceptionF<PERSON>er,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  ConsoleLogger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(private logger: ConsoleLogger) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';

    // Handle specific error types
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    } else if (exception instanceof Error) {
      // Handle Prisma UUID errors
      if (exception.message && exception.message.includes('UUID')) {
        status = HttpStatus.BAD_REQUEST;
        message = 'Invalid UUID format in request';
        this.logger.warn(
          `UUID error: ${exception.message} for path: ${request.url}`,
        );
      } else {
        this.logger.error(
          `Unhandled error: ${exception.message} for path: ${request.url}`,
        );
        this.logger.error(exception.stack);
      }
    }

    // Create consistent error response
    response.status(status).json({
      statusCode: status,
      message: message,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
