import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { WsGatewayAdapter } from './common/ws-gateway.adapter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConsoleLogger } from '@nestjs/common';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['warn', 'error', 'log', 'fatal', 'debug'],
  });

  // Enable CORS
  app.enableCors({ origin: '*' });

  // Swagger configuration
  const socketDoc = `
## WebSocket (Socket.io) API

**Connect to:**

    ws://localhost:3000/chat

**Authentication:**

Send your JWT token when connecting:

    const socket = io('ws://localhost:3000/chat', {
      auth: { token: '<your_jwt_token>' }
    });

### Example Events

#### 1. Get Paginated Room List (REST)

**Client requests:**

    GET /api/rooms?page=1&limit=20
    // Returns only rooms where you are the owner or a participant, paginated.

**Server responds:**

    [
      {
        _id: '...',
        name: 'Group',
        type: 'group',
        participants: ['...'],
        createdAt: '...',
        updatedAt: '...'
      },
      ...
    ]

#### 2. Join an Existing Room (Private or Group)

**Client emits:**

    socket.emit('room:join', { type: 'private', roomId: '<roomId>' });
    // or
    socket.emit('room:join', { type: 'group', roomId: '<roomId>' });

- roomId is required. You can only join existing rooms you are a participant of.

**Server responds:**

    socket.on('room:joined', ({ room, messages }) => {
      // room: room info
      // messages: latest N messages
    });

#### 3. Create and Join a New Room (Private or Group)

**Client emits:**

    // Private
    socket.emit('room:join', { type: 'private', userId: '<otherUserId>' });
    // Group
    socket.emit('room:join', { type: 'group', name: 'My Group', participants: ['userId1', 'userId2'] });

- If roomId is not provided, a new room is created and you are joined as owner/participant.

**Server responds:**

    socket.on('room:joined', ({ room, messages }) => { ... });

#### 4. Send a Message

**Client emits:**

    socket.emit('message:send', { roomId: '<roomId>', content: 'Hello!' });

**Server emits to all in room:**

    socket.on('message:new', (message) => { ... });

**Server emits to sender:**

    socket.on('message:delivered', (messageId) => { ... });

#### 5. Typing Indicator

**Client emits:**

    socket.emit('room:typing', { roomId: '<roomId>' });

**Server emits to room:**

    socket.on('room:typing', ({ userId }) => { ... });

#### 6. Leave a Room

**Client emits:**

    socket.emit('room:leave', '<roomId>');

**Server responds:**

    socket.on('room:left', (roomId) => { ... });

#### 7. User Online/Offline Events

**Server emits:**

    socket.on('user:online', (userId) => { ... });
    socket.on('user:offline', (userId) => { ... });
`;

  const config = new DocumentBuilder()
    .setTitle('Infotik Chat API')
    .setDescription(
      'REST API and WebSocket documentation for Infotik Chat\n\n' + socketDoc,
    )
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // Set up global exception filter
  const logger = new ConsoleLogger();
  app.useGlobalFilters(new GlobalExceptionFilter(logger));

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
