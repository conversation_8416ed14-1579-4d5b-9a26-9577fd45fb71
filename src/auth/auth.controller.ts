import { <PERSON>, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { AuthService } from './auth.service';

// DTO for registration
class RegisterDto {
  username: string;
  password: string;
}

// DTO for login
class LoginDto {
  username: string;
  password: string;
}

@ApiTags('Auth')
@Controller('api/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  // POST /api/auth/register
  @ApiOperation({ summary: 'Register a new user' })
  @ApiBody({
    type: RegisterDto,
    examples: {
      example1: {
        summary: 'Register Example',
        value: { username: 'alice', password: 'password123' },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Registration successful' })
  @Post('register')
  async register(@Body() dto: RegisterDto) {
    return this.authService.register(dto.username, dto.password);
  }

  // POST /api/auth/login
  @ApiOperation({ summary: 'Login and get JWT token' })
  @ApiBody({
    type: LoginDto,
    examples: {
      example1: {
        summary: 'Login Example',
        value: { username: 'alice', password: 'password123' },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'JWT token returned' })
  @Post('login')
  async login(@Body() dto: LoginDto) {
    return this.authService.login(dto.username, dto.password);
  }
}
