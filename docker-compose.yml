version: '3.8'

services:
  mongo:
    image: mongo:6.0
    container_name: infotik-chat
    restart: always
    ports:
      - '27017:27017'
    environment:
      MONGO_INITDB_DATABASE: infotik-chat
    volumes:
      - mongo_data:/data/db

  server:
    build: .
    container_name: infotik-server
    restart: always
    env_file:
      - .env
    ports:
      - '3000:3000'
    depends_on:
      - mongo
    environment:
      PORT: 3000
      MONGODB_URI: mongodb://mongo:27017/infotik-chat
      JWT_SECRET: your_jwt_secret_key
      JWT_EXPIRES_IN: 10000d
      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX: 100

volumes:
  mongo_data: 